# Created at 2025-08-04T18:12:26.167
System.exit() or native command error interrupted process checker.
java.lang.IllegalStateException: Cannot use PPID 23360 process information. Going to use NOOP events.
	at org.apache.maven.surefire.booter.PpidChecker.checkProcessInfo(PpidChecker.java:155)
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:116)
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)


