-------------------------------------------------------------------------------
Test set: com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest
-------------------------------------------------------------------------------
Tests run: 18, Failures: 18, Errors: 0, Skipped: 0, Time elapsed: 8.138 s <<< FAILURE! - in com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest
testGetTaxRatesByAgency_Success  Time elapsed: 0.2 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testGetTaxRatesByAgency_Success(TaxRateControllerTest.java:69)

testUpdateTaxRate_ValidationError  Time elapsed: 0.114 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testUpdateTaxRate_ValidationError(TaxRateControllerTest.java:209)

testDeleteTaxRate_Success  Time elapsed: 0.013 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testDeleteTaxRate_Success(TaxRateControllerTest.java:247)

testCreateTaxRate_Success  Time elapsed: 0.008 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<201> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testCreateTaxRate_Success(TaxRateControllerTest.java:129)

testCreateTaxRate_MissingRequiredFields  Time elapsed: 0.007 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testCreateTaxRate_MissingRequiredFields(TaxRateControllerTest.java:276)

testCreateTaxRate_DuplicateName  Time elapsed: 0.01 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testCreateTaxRate_DuplicateName(TaxRateControllerTest.java:315)

testUpdateTaxRate_NotFound  Time elapsed: 0.008 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testUpdateTaxRate_NotFound(TaxRateControllerTest.java:192)

testCreateTaxRate_ValidationError  Time elapsed: 0.014 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testCreateTaxRate_ValidationError(TaxRateControllerTest.java:146)

testGetTaxRatesByAgency_EmptyList  Time elapsed: 0.011 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testGetTaxRatesByAgency_EmptyList(TaxRateControllerTest.java:298)

testUpdateTaxRate_Success  Time elapsed: 0.013 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testUpdateTaxRate_Success(TaxRateControllerTest.java:175)

testDeactivateTaxRate_Success  Time elapsed: 0.01 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testDeactivateTaxRate_Success(TaxRateControllerTest.java:235)

testGetTaxRateById_Success  Time elapsed: 0.01 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testGetTaxRateById_Success(TaxRateControllerTest.java:101)

testActivateTaxRate_Success  Time elapsed: 0.013 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testActivateTaxRate_Success(TaxRateControllerTest.java:223)

testGetActiveTaxRatesByAgency_Success  Time elapsed: 0.006 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testGetActiveTaxRatesByAgency_Success(TaxRateControllerTest.java:86)

testGetTaxRateById_NotFound  Time elapsed: 0.01 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<401>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testGetTaxRateById_NotFound(TaxRateControllerTest.java:115)

testCreateTaxRate_InvalidPercentageRange  Time elapsed: 0.014 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testCreateTaxRate_InvalidPercentageRange(TaxRateControllerTest.java:288)

testDeleteTaxRate_InUse  Time elapsed: 0.012 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<409> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testDeleteTaxRate_InUse(TaxRateControllerTest.java:260)

testCreateTaxRate_InvalidInput  Time elapsed: 0.007 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at com.cap10mycap10.worklinkservice.controller.TaxRateControllerTest.testCreateTaxRate_InvalidInput(TaxRateControllerTest.java:162)

