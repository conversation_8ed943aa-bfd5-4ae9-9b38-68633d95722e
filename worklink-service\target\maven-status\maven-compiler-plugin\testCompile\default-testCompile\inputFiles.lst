E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\service\TaxRateServiceTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\mapper\InvoiceItemToInvoiceItemResultTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\service\TaxCalculationServiceTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\demo\EncryptionDemo.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\service\EmailSenderFactoryTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\service\EncryptionServiceTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\service\AgencyEmailConfigurationServiceTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\mapper\VehicleInventoryToVehicleInventoryDtoTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\dto\VehicleInventoryDtoSerializationTest.java
E:\Local Documents\GitHub\worklink-backend\worklink-service\src\test\java\com\cap10mycap10\worklinkservice\controller\TaxRateControllerTest.java
