package com.cap10mycap10.ouathservice.service;

import com.cap10mycap10.ouathservice.dto.AgencyEmailConfigurationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * Factory for creating JavaMailSender instances based on agency email configuration.
 * Creates fresh email senders for each request to ensure up-to-date configurations.
 */
@Component
@Slf4j
public class EmailSenderFactory {

    @Autowired
    private JavaMailSender defaultEmailSender;

    @Value("${env.companyName}")
    private String defaultCompanyName;

    @Value("${env.email}")
    private String defaultFromEmail;

    @Value("${env.supportEmail}")
    private String defaultSupportEmail;

    @Value("${env.website}")
    private String defaultWebsiteUrl;

    @Value("${env.companyLogo}")
    private String defaultLogoUrl;

    /**
     * Get JavaMailSender for agency or default.
     * Creates a fresh email sender each time to ensure up-to-date configuration.
     */
    public JavaMailSender getEmailSender(AgencyEmailConfigurationDto agencyConfig) {
        if (agencyConfig == null || !agencyConfig.isReadyToUse()) {
            log.debug("Using default email sender");
            return defaultEmailSender;
        }

        log.info("Creating new JavaMailSender for agency ID: {}", agencyConfig.getAgencyId());
        return createJavaMailSender(agencyConfig);
    }

    /**
     * Get email configuration for agency or default.
     * Creates fresh configuration each time to ensure up-to-date data.
     */
    public EmailConfiguration getEmailConfiguration(AgencyEmailConfigurationDto agencyConfig) {
        if (agencyConfig == null || !agencyConfig.isReadyToUse()) {
            return getDefaultEmailConfiguration();
        }

        log.debug("Creating email configuration for agency ID: {}", agencyConfig.getAgencyId());
        return EmailConfiguration.builder()
                .fromEmail(agencyConfig.getFromEmail())
                .fromName(agencyConfig.getFromName())
                .replyToEmail(agencyConfig.getReplyToEmail())
                .supportEmail(agencyConfig.getSupportEmail())
                .websiteUrl(agencyConfig.getWebsiteUrl())
                .logoUrl(agencyConfig.getLogoUrl())
                .build();
    }

    /**
     * Get default email configuration
     */
    public EmailConfiguration getDefaultEmailConfiguration() {
        return EmailConfiguration.builder()
                .fromEmail(defaultFromEmail)
                .fromName(defaultCompanyName)
                .replyToEmail(defaultFromEmail)
                .supportEmail(defaultSupportEmail)
                .websiteUrl(defaultWebsiteUrl)
                .logoUrl(defaultLogoUrl)
                .build();
    }



    private JavaMailSender createJavaMailSender(AgencyEmailConfigurationDto config) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        mailSender.setHost(config.getSmtpHost());
        mailSender.setPort(config.getSmtpPort());
        mailSender.setUsername(config.getSmtpUsername());
        mailSender.setPassword(config.getSmtpPassword());

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        
        if (Boolean.TRUE.equals(config.getSmtpAuth())) {
            props.put("mail.smtp.auth", "true");
        }
        
        if (Boolean.TRUE.equals(config.getSmtpStarttlsEnable())) {
            props.put("mail.smtp.starttls.enable", "true");
        }
        
        if (Boolean.TRUE.equals(config.getSmtpStarttlsRequired())) {
            props.put("mail.smtp.starttls.required", "true");
        }
        
        if (Boolean.TRUE.equals(config.getSmtpSslEnable())) {
            props.put("mail.smtp.ssl.enable", "true");
        }
        
        if (config.getSmtpSslSocketFactoryClass() != null) {
            props.put("mail.smtp.socketFactory.class", config.getSmtpSslSocketFactoryClass());
        }

        return mailSender;
    }

    /**
     * Email configuration holder
     */
    public static class EmailConfiguration {
        private String fromEmail;
        private String fromName;
        private String replyToEmail;
        private String supportEmail;
        private String websiteUrl;
        private String logoUrl;

        public static EmailConfigurationBuilder builder() {
            return new EmailConfigurationBuilder();
        }

        // Getters
        public String getFromEmail() { return fromEmail; }
        public String getFromName() { return fromName; }
        public String getReplyToEmail() { return replyToEmail; }
        public String getSupportEmail() { return supportEmail; }
        public String getWebsiteUrl() { return websiteUrl; }
        public String getLogoUrl() { return logoUrl; }

        public static class EmailConfigurationBuilder {
            private String fromEmail;
            private String fromName;
            private String replyToEmail;
            private String supportEmail;
            private String websiteUrl;
            private String logoUrl;

            public EmailConfigurationBuilder fromEmail(String fromEmail) {
                this.fromEmail = fromEmail;
                return this;
            }

            public EmailConfigurationBuilder fromName(String fromName) {
                this.fromName = fromName;
                return this;
            }

            public EmailConfigurationBuilder replyToEmail(String replyToEmail) {
                this.replyToEmail = replyToEmail;
                return this;
            }

            public EmailConfigurationBuilder supportEmail(String supportEmail) {
                this.supportEmail = supportEmail;
                return this;
            }

            public EmailConfigurationBuilder websiteUrl(String websiteUrl) {
                this.websiteUrl = websiteUrl;
                return this;
            }

            public EmailConfigurationBuilder logoUrl(String logoUrl) {
                this.logoUrl = logoUrl;
                return this;
            }

            public EmailConfiguration build() {
                EmailConfiguration config = new EmailConfiguration();
                config.fromEmail = this.fromEmail;
                config.fromName = this.fromName;
                config.replyToEmail = this.replyToEmail;
                config.supportEmail = this.supportEmail;
                config.websiteUrl = this.websiteUrl;
                config.logoUrl = this.logoUrl;
                return config;
            }
        }
    }
}
