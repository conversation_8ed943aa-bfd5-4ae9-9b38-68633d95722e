-------------------------------------------------------------------------------
Test set: com.cap10mycap10.worklinkservice.dto.VehicleInventoryDtoSerializationTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.054 s <<< FAILURE! - in com.cap10mycap10.worklinkservice.dto.VehicleInventoryDtoSerializationTest
testVehicleInventoryDtoSerialization  Time elapsed: 0.054 s  <<< ERROR!
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: 
Cannot construct instance of `java.time.LocalDate` (no Creators, like default constructor, exist): cannot deserialize from Object value (no delegate- or property-based Creator)
 at [Source: (String)"{"id":1,"dateInstalled":{"year":2024,"month":"JANUARY","monthValue":1,"dayOfMonth":1,"leapYear":true,"dayOfWeek":"MONDAY","dayOfYear":1,"era":"CE","chronology":{"id":"ISO","calendarType":"iso8601"}},"nextCheckDate":null,"name":"GPS Navigation","description":"Premium GPS navigation system","photoUrl1":null,"photoUrl2":null,"photoUrl3":null,"photoUrl4":null,"price":25.0,"taxExempt":false,"taxInclusive":true,"customTaxRate":{"id":1,"name":"Standard VAT","percentage":15.00,"description":"Standard VA"[truncated 479 chars]; line: 1, column: 26] (through reference chain: com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleInventoryDto["dateInstalled"])
	at com.cap10mycap10.worklinkservice.dto.VehicleInventoryDtoSerializationTest.testVehicleInventoryDtoSerialization(VehicleInventoryDtoSerializationTest.java:62)

