-------------------------------------------------------------------------------
Test set: com.cap10mycap10.worklinkservice.mapper.InvoiceItemToInvoiceItemResultTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 2, Errors: 0, Skipped: 0, Time elapsed: 0.054 s <<< FAILURE! - in com.cap10mycap10.worklinkservice.mapper.InvoiceItemToInvoiceItemResultTest
testConvert_WithDefaultTaxFields  Time elapsed: 0.048 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <0> but was: <1>
	at com.cap10mycap10.worklinkservice.mapper.InvoiceItemToInvoiceItemResultTest.testConvert_WithDefaultTaxFields(InvoiceItemToInvoiceItemResultTest.java:125)

testConvert_WithTaxExemptItem  Time elapsed: 0.002 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <0> but was: <1>
	at com.cap10mycap10.worklinkservice.mapper.InvoiceItemToInvoiceItemResultTest.testConvert_WithTaxExemptItem(InvoiceItemToInvoiceItemResultTest.java:96)

